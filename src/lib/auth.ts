import { betterAuth } from "better-auth";
import { admin, organization, openAPI } from "better-auth/plugins";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import db from "@/db";
import env from "@/env";

// Minimal Better Auth server instance. It will expose handlers we can mount under /api/auth.
export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "pg",
  }),
  baseURL: process.env.BETTER_AUTH_URL || `http://localhost:${env.PORT}`,
  secret: process.env.BETTER_AUTH_SECRET || "dev-secret-change-me",
  // Enable email/password auth as per server usage guide
  emailAndPassword: { 
    enabled: true, 
    autoSignIn: true ,
    requireEmailVerification: true,
  },
  // Allow requests from these origins to interact with Better Auth (e.g. cookies, redirects)
  trustedOrigins: env.CORS_ORIGINS,
  // Cache session payload in a signed cookie to avoid DB reads on session checks
  session: {
    cookieCache: { enabled: true, maxAge: 5 * 60 },
  },
  plugins: [admin(), organization(), openAPI()],
});

export type BetterAuth = typeof auth;


